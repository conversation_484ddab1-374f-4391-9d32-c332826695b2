name: Ha<PERSON>Ran<PERSON> DevOps Validation
on:
  workflow_dispatch:
    # accept inputs from the user for sudorank_os
    inputs:
      sudorank_os:
        description: "SudoRank OS"
        required: false
        default: "ubuntu22"
        type: choice
        options:
          - "ubuntu22"
          - "rhel8"
          - "agnostic"

env:
  SOLUTION_TOKEN: ${{ secrets.SOLUTION_TOKEN }}
  HACKERRANK_TOKEN: ${{ secrets.QUESTION_TOKEN }}
  HACKERRANK_STACKS: ${{ vars.HACKERRANK_STACKS }}
jobs:
  validate:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Validate repo
        run: |
          if [[ $REPO_NAME == [[:digit:]]* ]]
          then
            echo 'Repo is valid =>' $REPO_NAME
          else
            echo 'Repo not starting with digit =>' $REPO_NAME
            exit 1
          fi
        shell: bash
        env:
          REPO_NAME: ${{ github.event.repository.name }}
      - name: Checkout branch
        uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "pypy3.9"
      - run: pip install -r ./.github/scripts/requirements.txt
        name: Install script dependencies
      - run: python -u ./.github/scripts/validate_devops.py ${{ github.event.repository.name }}
        name: Validate DevOps
        env:
          SUDORANK_OS: ${{ inputs.sudorank_os }}
      - name: Find Pull Request
        uses: juliangruber/find-pull-request-action@v1.8.0
        id: find-pull-request
        with:
          branch: ${{ github.ref_name }}
      - name: Approve Pull Request if found
        uses: juliangruber/approve-pull-request-action@v2.0.4
        if: ${{ steps.find-pull-request.outputs.number != '' }}
        with:
          github-token: ${{ github.token }}
          number: ${{ steps.find-pull-request.outputs.number }}
