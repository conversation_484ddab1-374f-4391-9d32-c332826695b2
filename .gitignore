# See https://help.github.com/ignore-files/ for more about ignoring files.

# dependencies
/node_modules

# testing
/coverage
junit.xml

# production
/build

# distribution
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
/assets
*.swo
*.swp
.watchman*

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.idea
.env
!/.github/
!/.github/**
!/.github/
!/.github/**
!/.github/
!/.github/**
!/.github/
!/.github/**
