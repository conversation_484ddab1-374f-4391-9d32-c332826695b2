import React from "react";

class AutocorrectTextarea extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      textValue: "",
      lastProcessedValue: "",
    };
  }

  // Handle text change and apply corrections when a space is added
  handleTextChange = (newValue) => {
    const { corrections } = this.props;
    const { lastProcessedValue } = this.state;

    // If the value ends with a space, check if we need to apply corrections
    if (newValue.endsWith(' ')) {
      const words = newValue.trim().split(' ');
      if (words.length > 0) {
        const lastWord = words[words.length - 1];

        // Check if this word needs correction
        if (corrections && corrections[lastWord]) {
          // If this is the same value we processed before, apply the correction
          if (newValue === lastProcessedValue) {
            words[words.length - 1] = corrections[lastWord];
            const correctedValue = words.join(' ') + ' ';
            this.setState({
              textValue: correctedValue,
              lastProcessedValue: correctedValue
            });
            return;
          } else {
            // First time seeing this value, just store it
            this.setState({
              textValue: newValue,
              lastProcessedValue: newValue
            });
            return;
          }
        }
      }
    }

    // No correction needed, just update the value
    this.setState({
      textValue: newValue,
      lastProcessedValue: newValue
    });
  }

  render() {
    const { textValue } = this.state;
    return (
      <div className="text-center">
        <textarea
          data-testid="textarea"
          value={textValue}
          onChange={(e) => this.handleTextChange(e.target.value)}
          rows={10}
          cols={80}
          className="card"
        />
        <div>{textValue}</div>
      </div>
    );
  }
}

export default AutocorrectTextarea;
