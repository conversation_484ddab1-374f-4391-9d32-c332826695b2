import React from "react";

class AutocorrectTextarea extends React.Component {
  constructor() {
    super();
    this.state = {
      textValue: "abc ",
      corrections: {
        realy: "really",
        wierd: "weird",
        nmae: "name",
        ax: "x",
        xy: "x",
        y: "z",
        abc: "bc",
      },
    };
  }

  // handle correctuon func
  // handleCorrectionOnInput() {
  //   const { corrections, textValue } = this.state;
  //   let correctText = "";

  //   if (textValue.includes(" ")) {
  //     console.log(textValue, textValue.length);
  //     // convert to array
  //     const textValArr = textValue.split(" ");

  //     console.log(textValArr, textValArr.length);

  //     // get last item in string
  //     const textValArrLast = textValArr[textValArr.length - 2];

  //     console.log(textValArrLast, textValArr.length);

  //     for (let key in corrections) {
  //       console.log("key", key, textValArrLast);
  //       if (key === textValArrLast) {
  //         correctText = corrections[key];
  //       }
  //     }

  //     if (correctText !== "") {
  //       textValArr.pop();
  //       textValArr.pop();
  //       textValArr.push(correctText);
  //       console.log(textValArr, correctText)
  //       this.setState({ textValue: textValArr.join(" ") });
  //     } else {
  //       this.setState({ textValue: textValue });
  //     }
  //   }
  // }

  // handle correctuon func
  handleCorrection() {
    console.log("handleCorrection", this.state.textValue);

    const { corrections, textValue } = this.state;
    let correctText = "";

    // convert to array
    const textValArr = textValue.split(" ");

    // get last item in string
    const textValArrLast = textValArr[textValArr.length - 1];

    for (let key in corrections) {
      if (key === textValArrLast) {
        correctText = corrections[key];
      }
    }

    if (correctText !== "") {
      textValArr.pop();
      textValArr.push(correctText);
      this.setState({ textValue: textValArr.join(" ") });
    }
  }

  handleCorrectionPaste(textValue) {
    console.log("handleCorrectionPaste", textValue);

    const { corrections } = this.state;
    let correctText = "";

    // convert to array
    const textValArr = textValue.trim().split(" ");
    console.log(textValArr);

    // get last item in string
    const textValArrLast = textValArr[textValArr.length - 1];
    console.log(textValArrLast);

    for (let key in corrections) {
      if (key === textValArrLast) {
        correctText = corrections[key];
      }
    }

    if (correctText !== "") {
      textValArr.pop();
      textValArr.push(correctText);
      console.log(textValArr);

      console.log(textValArr.join(" "));

      this.setState({ textValue: textValArr.join(" ") });
    }
  }

  componentDidUpdate() {
    console.log("render", this.state.textValue);
  }

  // listen if user press space key
  handleKeyDown(key) {
    if (key === " ") {
      this.handleCorrection();
    }
  }

  render() {
    const { textValue } = this.state;
    return (
      <div className="text-center">
        <textarea
          data-testid="textarea"
          value={textValue}
          onChange={(e) => this.setState({ textValue: e.target.value })}
          onKeyDown={(e) => {
            if (e.key === " ") {
              this.handleCorrection();
            }
          }}
          onPaste={(e) => {
            const text = e.clipboardData.getData("text/plain");
            this.handleCorrectionPaste(text);
          }}
          rows={10}
          cols={80}
          className="card"
        />
        <div>{textValue}</div>
      </div>
    );
  }
}

export default AutocorrectTextarea;
